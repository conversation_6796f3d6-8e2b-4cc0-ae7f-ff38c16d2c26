{"version": 3, "sources": ["../../src/server/image-optimizer.ts"], "names": ["createHash", "promises", "cpus", "mediaType", "contentDisposition", "getOrientation", "Orientation", "imageSizeOf", "isAnimated", "join", "nodeUrl", "getImageBlurSvg", "hasLocalMatch", "hasRemoteMatch", "createRequestResponseMocks", "sendEtagResponse", "getContentType", "getExtension", "Log", "parseUrl", "AVIF", "WEBP", "PNG", "JPEG", "JXL", "JP2", "HEIC", "GIF", "SVG", "ICO", "ICNS", "TIFF", "BMP", "PDF", "CACHE_VERSION", "ANIMATABLE_TYPES", "BYPASS_TYPES", "BLUR_IMG_SIZE", "BLUR_QUALITY", "sharp", "require", "process", "env", "NEXT_SHARP_PATH", "concurrency", "divisor", "NODE_ENV", "Math", "floor", "max", "length", "e", "showSharpMissingWarning", "getSupportedMimeType", "options", "accept", "mimeType", "includes", "getHash", "items", "hash", "item", "update", "String", "digest", "replace", "writeToCacheDir", "dir", "extension", "maxAge", "expireAt", "buffer", "etag", "filename", "rm", "recursive", "force", "catch", "mkdir", "writeFile", "detectContentType", "every", "b", "i", "meta", "metadata", "_", "format", "ImageOptimizerCache", "validateParams", "req", "query", "nextConfig", "isDev", "imageData", "images", "deviceSizes", "imageSizes", "domains", "minimumCacheTTL", "formats", "remotePatterns", "localPatterns", "qualities", "url", "w", "q", "href", "warnOnce", "errorMessage", "Array", "isArray", "startsWith", "isAbsolute", "test", "decodeURIComponent", "pathname", "hrefParsed", "URL", "toString", "_error", "protocol", "width", "parseInt", "isNaN", "sizes", "push", "isValidSize", "quality", "headers", "isStatic", "basePath", "get<PERSON><PERSON><PERSON><PERSON>", "constructor", "distDir", "cacheDir", "get", "cache<PERSON>ey", "files", "readdir", "now", "Date", "file", "maxAgeSt", "expireAtSt", "split", "readFile", "Number", "value", "kind", "revalidateAfter", "curRevalidate", "isStale", "set", "revalidate", "Error", "err", "error", "ImageError", "statusCode", "message", "parseCacheControl", "str", "map", "Map", "directive", "key", "trim", "toLowerCase", "getMaxAge", "age", "endsWith", "slice", "n", "optimizeImage", "contentType", "height", "nextConfigOutput", "optimizedBuffer", "transformer", "sequentialRead", "rotate", "resize", "undefined", "withoutEnlargement", "avif", "avifQuality", "chromaSubsampling", "webp", "png", "jpeg", "progressive", "<PERSON><PERSON><PERSON><PERSON>", "orientation", "operations", "RIGHT_TOP", "type", "numRotations", "BOTTOM_RIGHT", "LEFT_BOTTOM", "processBuffer", "fetchExternalImage", "res", "fetch", "ok", "status", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "cacheControl", "fetchInternalImage", "_req", "_res", "handleRequest", "mocked", "method", "socket", "parse", "hasStreamed", "concat", "buffers", "<PERSON><PERSON><PERSON><PERSON>", "imageOptimizer", "imageUpstream", "paramsResult", "upstreamBuffer", "upstreamType", "dangerouslyAllowSVG", "output", "getMetadata", "opts", "blur<PERSON>idth", "blurHeight", "blurDataURL", "unescape", "getFileNameWithExtension", "urlWithoutQueryParams", "fileNameWithExtension", "pop", "fileName", "setResponseHeaders", "xCache", "imagesConfig", "<PERSON><PERSON><PERSON><PERSON>", "finished", "contentDispositionType", "contentSecurityPolicy", "sendResponse", "result", "byteLength", "end", "getImageSize", "decodeBuffer"], "mappings": "AAAA,SAASA,UAAU,QAAQ,SAAQ;AACnC,SAASC,QAAQ,QAAQ,KAAI;AAC7B,SAASC,IAAI,QAAQ,KAAI;AAEzB,SAASC,SAAS,QAAQ,kCAAiC;AAC3D,OAAOC,wBAAwB,yCAAwC;AACvE,SAASC,cAAc,EAAEC,WAAW,QAAQ,qCAAoC;AAChF,OAAOC,iBAAiB,gCAA+B;AACvD,OAAOC,gBAAgB,iCAAgC;AACvD,SAASC,IAAI,QAAQ,OAAM;AAC3B,OAAOC,aAA0C,MAAK;AAEtD,SAASC,eAAe,QAAQ,+BAA8B;AAE9D,SAASC,aAAa,QAAQ,oCAAmC;AACjE,SAASC,cAAc,QAAQ,qCAAoC;AAEnE,SAASC,0BAA0B,QAAQ,qBAAoB;AAW/D,SAASC,gBAAgB,QAAQ,iBAAgB;AACjD,SAASC,cAAc,EAAEC,YAAY,QAAQ,iBAAgB;AAC7D,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,QAAQ,QAAQ,aAAY;AAIrC,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;IAACd;IAAMC;IAAKK;CAAI;AACzC,MAAMS,eAAe;IAACR;IAAKC;IAAKC;IAAME;IAAKR;IAAKE;CAAK;AACrD,MAAMW,gBAAgB,EAAE,mCAAmC;;AAC3D,MAAMC,eAAe,GAAG,mCAAmC;;AAE3D,IAAIC;AAEJ,IAAI;IACFA,QAAQC,QAAQC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAC/C,IAAIJ,SAASA,MAAMK,WAAW,KAAK,GAAG;QACpC,2DAA2D;QAC3D,8DAA8D;QAC9D,0DAA0D;QAC1D,MAAMC,UAAUJ,QAAQC,GAAG,CAACI,QAAQ,KAAK,gBAAgB,IAAI;QAC7DP,MAAMK,WAAW,CAACG,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAAC/C,OAAOgD,MAAM,GAAGL,SAAS;IACjE;AACF,EAAE,OAAOM,GAAG;AACV,iEAAiE;AACnE;AAEA,IAAIC,0BAA0BX,QAAQC,GAAG,CAACI,QAAQ,KAAK;AAmBvD,SAASO,qBAAqBC,OAAiB,EAAEC,SAAS,EAAE;IAC1D,MAAMC,WAAWrD,UAAUoD,QAAQD;IACnC,OAAOC,OAAOE,QAAQ,CAACD,YAAYA,WAAW;AAChD;AAEA,OAAO,SAASE,QAAQC,KAAmC;IACzD,MAAMC,OAAO5D,WAAW;IACxB,KAAK,IAAI6D,QAAQF,MAAO;QACtB,IAAI,OAAOE,SAAS,UAAUD,KAAKE,MAAM,CAACC,OAAOF;aAC5C;YACHD,KAAKE,MAAM,CAACD;QACd;IACF;IACA,qDAAqD;IACrD,OAAOD,KAAKI,MAAM,CAAC,UAAUC,OAAO,CAAC,OAAO;AAC9C;AAEA,eAAeC,gBACbC,GAAW,EACXC,SAAiB,EACjBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdC,IAAY;IAEZ,MAAMC,WAAWhE,KAAK0D,KAAK,CAAC,EAAEE,OAAO,CAAC,EAAEC,SAAS,CAAC,EAAEE,KAAK,CAAC,EAAEJ,UAAU,CAAC;IAEvE,MAAMnE,SAASyE,EAAE,CAACP,KAAK;QAAEQ,WAAW;QAAMC,OAAO;IAAK,GAAGC,KAAK,CAAC,KAAO;IAEtE,MAAM5E,SAAS6E,KAAK,CAACX,KAAK;QAAEQ,WAAW;IAAK;IAC5C,MAAM1E,SAAS8E,SAAS,CAACN,UAAUF;AACrC;AAEA;;;;CAIC,GACD,OAAO,eAAeS,kBACpBT,MAAc;IAEd,IAAI;QAAC;QAAM;QAAM;KAAK,CAACU,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QACvD,OAAO3D;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC0D,KAAK,CACpD,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAE1B;QACA,OAAO5D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC2D,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QAC7D,OAAOvD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;KAAK,CAACsD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKX,MAAM,CAACY,EAAE,KAAKD,IAEhC;QACA,OAAO7D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC4D,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QACnE,OAAOtD;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACqD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKX,MAAM,CAACY,EAAE,KAAKD,IAEhC;QACA,OAAO9D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC6D,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QAC7D,OAAOrD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACoD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QAC7D,OAAOpD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACmD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QAC7D,OAAOnD;IACT;IACA,IAAI;QAAC;QAAM;KAAK,CAACkD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QACjD,OAAOlD;IACT;IACA,IAAI;QAAC;QAAM;KAAK,CAACiD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QACjD,OAAO1D;IACT;IACA,IACE;QACE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KACnE,CAACyD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAChC;QACA,OAAO1D;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACyD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKX,MAAM,CAACY,EAAE,KAAKD,IAEhC;QACA,OAAOxD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAACuD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAAI;QACnE,OAAOjD;IACT;IACA,IACE;QACE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KACnE,CAACgD,KAAK,CAAC,CAACC,GAAGC,IAAMZ,MAAM,CAACY,EAAE,KAAKD,IAChC;QACA,OAAOzD;IACT;IAEA,iCAAiC;IACjC,IAAIc,OAAO;QACT,MAAM6C,OAAO,MAAM7C,MAAMgC,QACtBc,QAAQ,GACRR,KAAK,CAAC,CAACS,IAAM;QAChB,OAAQF,wBAAAA,KAAMG,MAAM;YAClB,KAAK;gBACH,OAAOnE;YACT,KAAK;gBACH,OAAOC;YACT,KAAK;gBACH,OAAOC;YACT,KAAK;YACL,KAAK;gBACH,OAAOC;YACT,KAAK;gBACH,OAAOI;YACT,KAAK;gBACH,OAAOC;YACT,KAAK;YACL,KAAK;gBACH,OAAOG;YACT,KAAK;gBACH,OAAOL;YACT;gBACE,OAAO;QACX;IACF;IAEA,OAAO;AACT;AAEA,OAAO,MAAM8D;IAIX,OAAOC,eACLC,GAAoB,EACpBC,KAAkC,EAClCC,UAA8B,EAC9BC,KAAc,EACgC;YASvBD,oBACDA,qBACJA;QAVlB,MAAME,YAAYF,WAAWG,MAAM;QACnC,MAAM,EACJC,cAAc,EAAE,EAChBC,aAAa,EAAE,EACfC,UAAU,EAAE,EACZC,kBAAkB,EAAE,EACpBC,UAAU;YAAC;SAAa,EACzB,GAAGN;QACJ,MAAMO,iBAAiBT,EAAAA,qBAAAA,WAAWG,MAAM,qBAAjBH,mBAAmBS,cAAc,KAAI,EAAE;QAC9D,MAAMC,iBAAgBV,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBU,aAAa;QACtD,MAAMC,aAAYX,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBW,SAAS;QAC9C,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGf;QACtB,IAAIgB;QAEJ,IAAIT,QAAQhD,MAAM,GAAG,GAAG;YACtBhC,IAAI0F,QAAQ,CACV;QAEJ;QAEA,IAAI,CAACJ,KAAK;YACR,OAAO;gBAAEK,cAAc;YAA8B;QACvD,OAAO,IAAIC,MAAMC,OAAO,CAACP,MAAM;YAC7B,OAAO;gBAAEK,cAAc;YAAqC;QAC9D;QAEA,IAAIL,IAAItD,MAAM,GAAG,MAAM;YACrB,OAAO;gBAAE2D,cAAc;YAA8B;QACvD;QAEA,IAAIL,IAAIQ,UAAU,CAAC,OAAO;YACxB,OAAO;gBACLH,cAAc;YAChB;QACF;QAEA,IAAII;QAEJ,IAAIT,IAAIQ,UAAU,CAAC,MAAM;gBAKA7F;YAJvBwF,OAAOH;YACPS,aAAa;YACb,IACE,uBAAuBC,IAAI,CACzBC,mBAAmBhG,EAAAA,YAAAA,SAASqF,yBAATrF,UAAeiG,QAAQ,KAAI,MAEhD;gBACA,OAAO;oBACLP,cAAc;gBAChB;YACF;YACA,IAAI,CAACjG,cAAc0F,eAAeE,MAAM;gBACtC,OAAO;oBAAEK,cAAc;gBAAiC;YAC1D;QACF,OAAO;YACL,IAAIQ;YAEJ,IAAI;gBACFA,aAAa,IAAIC,IAAId;gBACrBG,OAAOU,WAAWE,QAAQ;gBAC1BN,aAAa;YACf,EAAE,OAAOO,QAAQ;gBACf,OAAO;oBAAEX,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC;gBAAC;gBAAS;aAAS,CAACpD,QAAQ,CAAC4D,WAAWI,QAAQ,GAAG;gBACtD,OAAO;oBAAEZ,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAChG,eAAeqF,SAASG,gBAAgBgB,aAAa;gBACxD,OAAO;oBAAER,cAAc;gBAAiC;YAC1D;QACF;QAEA,IAAI,CAACJ,GAAG;YACN,OAAO;gBAAEI,cAAc;YAAoC;QAC7D,OAAO,IAAIC,MAAMC,OAAO,CAACN,IAAI;YAC3B,OAAO;gBAAEI,cAAc;YAA2C;QACpE;QAEA,IAAI,CAACH,GAAG;YACN,OAAO;gBAAEG,cAAc;YAAsC;QAC/D,OAAO,IAAIC,MAAMC,OAAO,CAACL,IAAI;YAC3B,OAAO;gBAAEG,cAAc;YAA6C;QACtE;QAEA,MAAMa,QAAQC,SAASlB,GAAG;QAE1B,IAAIiB,SAAS,KAAKE,MAAMF,QAAQ;YAC9B,OAAO;gBACLb,cAAc;YAChB;QACF;QAEA,MAAMgB,QAAQ;eAAK7B,eAAe,EAAE;eAAOC,cAAc,EAAE;SAAE;QAE7D,IAAIJ,OAAO;YACTgC,MAAMC,IAAI,CAACzF;QACb;QAEA,MAAM0F,cACJF,MAAMpE,QAAQ,CAACiE,UAAW7B,SAAS6B,SAASrF;QAE9C,IAAI,CAAC0F,aAAa;YAChB,OAAO;gBACLlB,cAAc,CAAC,yBAAyB,EAAEa,MAAM,eAAe,CAAC;YAClE;QACF;QAEA,MAAMM,UAAUL,SAASjB;QAEzB,IAAIkB,MAAMI,YAAYA,UAAU,KAAKA,UAAU,KAAK;YAClD,OAAO;gBACLnB,cACE;YACJ;QACF;QAEA,IAAIN,WAAW;YACb,IAAIV,OAAO;gBACTU,UAAUuB,IAAI,CAACxF;YACjB;YAEA,IAAI,CAACiE,UAAU9C,QAAQ,CAACuE,UAAU;gBAChC,OAAO;oBACLnB,cAAc,CAAC,2BAA2B,EAAEH,EAAE,eAAe,CAAC;gBAChE;YACF;QACF;QAEA,MAAMlD,WAAWH,qBAAqB+C,WAAW,EAAE,EAAEV,IAAIuC,OAAO,CAAC,SAAS;QAE1E,MAAMC,WAAW1B,IAAIQ,UAAU,CAC7B,CAAC,EAAEpB,WAAWuC,QAAQ,IAAI,GAAG,mBAAmB,CAAC;QAGnD,OAAO;YACLxB;YACAkB;YACAZ;YACAiB;YACAR;YACAM;YACAxE;YACA2C;QACF;IACF;IAEA,OAAOiC,YAAY,EACjBzB,IAAI,EACJe,KAAK,EACLM,OAAO,EACPxE,QAAQ,EAMT,EAAU;QACT,OAAOE,QAAQ;YAACxB;YAAeyE;YAAMe;YAAOM;YAASxE;SAAS;IAChE;IAEA6E,YAAY,EACVC,OAAO,EACP1C,UAAU,EAIX,CAAE;QACD,IAAI,CAAC2C,QAAQ,GAAG9H,KAAK6H,SAAS,SAAS;QACvC,IAAI,CAAC1C,UAAU,GAAGA;IACpB;IAEA,MAAM4C,IAAIC,QAAgB,EAAyC;QACjE,IAAI;YACF,MAAMF,WAAW9H,KAAK,IAAI,CAAC8H,QAAQ,EAAEE;YACrC,MAAMC,QAAQ,MAAMzI,SAAS0I,OAAO,CAACJ;YACrC,MAAMK,MAAMC,KAAKD,GAAG;YAEpB,KAAK,MAAME,QAAQJ,MAAO;gBACxB,MAAM,CAACK,UAAUC,YAAYxE,MAAMJ,UAAU,GAAG0E,KAAKG,KAAK,CAAC,KAAK;gBAChE,MAAM1E,SAAS,MAAMtE,SAASiJ,QAAQ,CAACzI,KAAK8H,UAAUO;gBACtD,MAAMxE,WAAW6E,OAAOH;gBACxB,MAAM3E,SAAS8E,OAAOJ;gBAEtB,OAAO;oBACLK,OAAO;wBACLC,MAAM;wBACN7E;wBACAD;wBACAH;oBACF;oBACAkF,iBACEvG,KAAKE,GAAG,CAACoB,QAAQ,IAAI,CAACuB,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC3D0C,KAAKD,GAAG;oBACVW,eAAelF;oBACfmF,SAASZ,MAAMtE;gBACjB;YACF;QACF,EAAE,OAAOgB,GAAG;QACV,qDAAqD;QACvD;QACA,OAAO;IACT;IACA,MAAMmE,IACJhB,QAAgB,EAChBW,KAAmC,EACnC,EACEM,UAAU,EAGX,EACD;QACA,IAAIN,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,SAAS;YAC3B,MAAM,IAAIM,MAAM;QAClB;QAEA,IAAI,OAAOD,eAAe,UAAU;YAClC,MAAM,IAAIC,MAAM;QAClB;QACA,MAAMrF,WACJvB,KAAKE,GAAG,CAACyG,YAAY,IAAI,CAAC9D,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC/D0C,KAAKD,GAAG;QAEV,IAAI;YACF,MAAM1E,gBACJzD,KAAK,IAAI,CAAC8H,QAAQ,EAAEE,WACpBW,MAAMhF,SAAS,EACfsF,YACApF,UACA8E,MAAM7E,MAAM,EACZ6E,MAAM5E,IAAI;QAEd,EAAE,OAAOoF,KAAK;YACZ1I,IAAI2I,KAAK,CAAC,CAAC,+BAA+B,EAAEpB,SAAS,CAAC,EAAEmB;QAC1D;IACF;AACF;AACA,OAAO,MAAME,mBAAmBH;IAG9BtB,YAAY0B,UAAkB,EAAEC,OAAe,CAAE;QAC/C,KAAK,CAACA;QAEN,uCAAuC;QACvC,IAAID,cAAc,KAAK;YACrB,IAAI,CAACA,UAAU,GAAGA;QACpB,OAAO;YACL,IAAI,CAACA,UAAU,GAAG;QACpB;IACF;AACF;AAEA,SAASE,kBACPC,GAA8B;IAE9B,MAAMC,MAAM,IAAIC;IAChB,IAAI,CAACF,KAAK;QACR,OAAOC;IACT;IACA,KAAK,IAAIE,aAAaH,IAAIjB,KAAK,CAAC,KAAM;QACpC,IAAI,CAACqB,KAAKlB,MAAM,GAAGiB,UAAUE,IAAI,GAAGtB,KAAK,CAAC,KAAK;QAC/CqB,MAAMA,IAAIE,WAAW;QACrB,IAAIpB,OAAO;YACTA,QAAQA,MAAMoB,WAAW;QAC3B;QACAL,IAAIV,GAAG,CAACa,KAAKlB;IACf;IACA,OAAOe;AACT;AAEA,OAAO,SAASM,UAAUP,GAA8B;IACtD,MAAMC,MAAMF,kBAAkBC;IAC9B,IAAIC,KAAK;QACP,IAAIO,MAAMP,IAAI3B,GAAG,CAAC,eAAe2B,IAAI3B,GAAG,CAAC,cAAc;QACvD,IAAIkC,IAAI1D,UAAU,CAAC,QAAQ0D,IAAIC,QAAQ,CAAC,MAAM;YAC5CD,MAAMA,IAAIE,KAAK,CAAC,GAAG,CAAC;QACtB;QACA,MAAMC,IAAIlD,SAAS+C,KAAK;QACxB,IAAI,CAAC9C,MAAMiD,IAAI;YACb,OAAOA;QACT;IACF;IACA,OAAO;AACT;AAEA,OAAO,eAAeC,cAAc,EAClCvG,MAAM,EACNwG,WAAW,EACX/C,OAAO,EACPN,KAAK,EACLsD,MAAM,EACNC,gBAAgB,EAQjB;IACC,IAAIC,kBAAkB3G;IACtB,IAAIhC,OAAO;QACT,mCAAmC;QACnC,MAAM4I,cAAc5I,MAAMgC,QAAQ;YAChC6G,gBAAgB;QAClB;QAEAD,YAAYE,MAAM;QAElB,IAAIL,QAAQ;YACVG,YAAYG,MAAM,CAAC5D,OAAOsD;QAC5B,OAAO;YACLG,YAAYG,MAAM,CAAC5D,OAAO6D,WAAW;gBACnCC,oBAAoB;YACtB;QACF;QAEA,IAAIT,gBAAgB3J,MAAM;YACxB,IAAI+J,YAAYM,IAAI,EAAE;gBACpB,MAAMC,cAAc1D,UAAU;gBAC9BmD,YAAYM,IAAI,CAAC;oBACfzD,SAASjF,KAAKE,GAAG,CAACyI,aAAa;oBAC/BC,mBAAmB;gBACrB;YACF,OAAO;gBACLzK,IAAI0F,QAAQ,CACV,CAAC,wIAAwI,CAAC,GACxI;gBAEJuE,YAAYS,IAAI,CAAC;oBAAE5D;gBAAQ;YAC7B;QACF,OAAO,IAAI+C,gBAAgB1J,MAAM;YAC/B8J,YAAYS,IAAI,CAAC;gBAAE5D;YAAQ;QAC7B,OAAO,IAAI+C,gBAAgBzJ,KAAK;YAC9B6J,YAAYU,GAAG,CAAC;gBAAE7D;YAAQ;QAC5B,OAAO,IAAI+C,gBAAgBxJ,MAAM;YAC/B4J,YAAYW,IAAI,CAAC;gBAAE9D;gBAAS+D,aAAa;YAAK;QAChD;QAEAb,kBAAkB,MAAMC,YAAYa,QAAQ;IAC5C,iCAAiC;IACnC,OAAO;QACL,IAAI5I,2BAA2B6H,qBAAqB,cAAc;YAChE/J,IAAI2I,KAAK,CACP,CAAC,0LAA0L,CAAC;YAE9L,MAAM,IAAIC,WAAW,KAAK;QAC5B;QACA,wCAAwC;QACxC,IAAI1G,yBAAyB;YAC3BlC,IAAI0F,QAAQ,CACV,CAAC,wLAAwL,CAAC,GACxL;YAEJxD,0BAA0B;QAC5B;QAEA,qCAAqC;QACrC,MAAM6I,cAAc,MAAM5L,eAAekE;QAEzC,MAAM2H,aAA0B,EAAE;QAElC,IAAID,gBAAgB3L,YAAY6L,SAAS,EAAE;YACzCD,WAAWpE,IAAI,CAAC;gBAAEsE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIJ,gBAAgB3L,YAAYgM,YAAY,EAAE;YACnDJ,WAAWpE,IAAI,CAAC;gBAAEsE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIJ,gBAAgB3L,YAAYiM,WAAW,EAAE;YAClDL,WAAWpE,IAAI,CAAC;gBAAEsE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO;QACL,kCAAkC;QAClC,6DAA6D;QAC7D,+BAA+B;QACjC;QAEA,IAAIrB,QAAQ;YACVkB,WAAWpE,IAAI,CAAC;gBAAEsE,MAAM;gBAAU1E;gBAAOsD;YAAO;QAClD,OAAO;YACLkB,WAAWpE,IAAI,CAAC;gBAAEsE,MAAM;gBAAU1E;YAAM;QAC1C;QAEA,MAAM,EAAE8E,aAAa,EAAE,GACrBhK,QAAQ;QAEV,IAAIuI,gBAAgB3J,MAAM;YACxB8J,kBAAkB,MAAMsB,cAAcjI,QAAQ2H,YAAY,QAAQlE;QACpE,OAAO,IAAI+C,gBAAgB1J,MAAM;YAC/B6J,kBAAkB,MAAMsB,cAAcjI,QAAQ2H,YAAY,QAAQlE;QACpE,OAAO,IAAI+C,gBAAgBzJ,KAAK;YAC9B4J,kBAAkB,MAAMsB,cAAcjI,QAAQ2H,YAAY,OAAOlE;QACnE,OAAO,IAAI+C,gBAAgBxJ,MAAM;YAC/B2J,kBAAkB,MAAMsB,cAAcjI,QAAQ2H,YAAY,QAAQlE;QACpE;IACF;IAEA,OAAOkD;AACT;AAEA,OAAO,eAAeuB,mBAAmB9F,IAAY;IACnD,MAAM+F,MAAM,MAAMC,MAAMhG;IAExB,IAAI,CAAC+F,IAAIE,EAAE,EAAE;QACX1L,IAAI2I,KAAK,CAAC,sCAAsClD,MAAM+F,IAAIG,MAAM;QAChE,MAAM,IAAI/C,WACR4C,IAAIG,MAAM,EACV;IAEJ;IAEA,MAAMtI,SAASuI,OAAOC,IAAI,CAAC,MAAML,IAAIM,WAAW;IAChD,MAAMjC,cAAc2B,IAAIzE,OAAO,CAACO,GAAG,CAAC;IACpC,MAAMyE,eAAeP,IAAIzE,OAAO,CAACO,GAAG,CAAC;IAErC,OAAO;QAAEjE;QAAQwG;QAAakC;IAAa;AAC7C;AAEA,OAAO,eAAeC,mBACpBvG,IAAY,EACZwG,IAAqB,EACrBC,IAAoB,EACpBC,aAIkB;IAElB,IAAI;QACF,MAAMC,SAASxM,2BAA2B;YACxC0F,KAAKG;YACL4G,QAAQJ,KAAKI,MAAM,IAAI;YACvBC,QAAQL,KAAKK,MAAM;QACrB;QAEA,MAAMH,cAAcC,OAAO5H,GAAG,EAAE4H,OAAOZ,GAAG,EAAEhM,QAAQ+M,KAAK,CAAC9G,MAAM;QAChE,MAAM2G,OAAOZ,GAAG,CAACgB,WAAW;QAE5B,IAAI,CAACJ,OAAOZ,GAAG,CAAC3C,UAAU,EAAE;YAC1B7I,IAAI2I,KAAK,CAAC,6BAA6BlD,MAAM2G,OAAOZ,GAAG,CAAC3C,UAAU;YAClE,MAAM,IAAID,WACRwD,OAAOZ,GAAG,CAAC3C,UAAU,EACrB;QAEJ;QAEA,MAAMxF,SAASuI,OAAOa,MAAM,CAACL,OAAOZ,GAAG,CAACkB,OAAO;QAC/C,MAAM7C,cAAcuC,OAAOZ,GAAG,CAACmB,SAAS,CAAC;QACzC,MAAMZ,eAAeK,OAAOZ,GAAG,CAACmB,SAAS,CAAC;QAC1C,OAAO;YAAEtJ;YAAQwG;YAAakC;QAAa;IAC7C,EAAE,OAAOrD,KAAK;QACZ1I,IAAI2I,KAAK,CAAC,sCAAsClD,MAAMiD;QACtD,MAAM,IAAIE,WACR,KACA;IAEJ;AACF;AAEA,OAAO,eAAegE,eACpBC,aAA4B,EAC5BC,YAGC,EACDpI,UAMC,EACDC,KAA0B;IAE1B,MAAM,EAAEc,IAAI,EAAEqB,OAAO,EAAEN,KAAK,EAAElE,QAAQ,EAAE,GAAGwK;IAC3C,MAAMC,iBAAiBF,cAAcxJ,MAAM;IAC3C,MAAMF,SAASoG,UAAUsD,cAAcd,YAAY;IACnD,MAAMiB,eAAe,MAAMlJ,kBAAkBiJ;IAE7C,IACE,CAACC,gBACD,CAACA,aAAalH,UAAU,CAAC,aACzBkH,aAAazK,QAAQ,CAAC,MACtB;QACAvC,IAAI2I,KAAK,CACP,kDACAlD,MACA,YACAuH;QAEF,MAAM,IAAIpE,WAAW,KAAK;IAC5B;IACA,IACEoE,aAAalH,UAAU,CAAC,gBACxB,CAACpB,WAAWG,MAAM,CAACoI,mBAAmB,EACtC;QACAjN,IAAI2I,KAAK,CACP,CAAC,wBAAwB,EAAElD,KAAK,YAAY,EAAEuH,aAAa,qCAAqC,CAAC;QAEnG,MAAM,IAAIpE,WACR,KACA;IAEJ;IACA,IAAI3H,iBAAiBsB,QAAQ,CAACyK,iBAAiB1N,WAAWyN,iBAAiB;QACzE/M,IAAI0F,QAAQ,CACV,CAAC,wBAAwB,EAAED,KAAK,8GAA8G,CAAC;QAEjJ,OAAO;YAAEpC,QAAQ0J;YAAgBlD,aAAamD;YAAc7J;QAAO;IACrE;IACA,IAAIjC,aAAaqB,QAAQ,CAACyK,eAAe;QACvC,OAAO;YAAE3J,QAAQ0J;YAAgBlD,aAAamD;YAAc7J;QAAO;IACrE;IAEA,IAAI0G;IAEJ,IAAIvH,UAAU;QACZuH,cAAcvH;IAChB,OAAO,IACLvC,aAAaiN,iBACbA,iBAAiB7M,QACjB6M,iBAAiB9M,MACjB;QACA2J,cAAcmD;IAChB,OAAO;QACLnD,cAAcxJ;IAChB;IACA,IAAI;QACF,IAAI2J,kBAAkB,MAAMJ,cAAc;YACxCvG,QAAQ0J;YACRlD;YACA/C;YACAN;YACAuD,kBAAkBrF,WAAWwI,MAAM;QACrC;QACA,IAAIlD,iBAAiB;YACnB,IAAIrF,SAAS6B,SAASrF,iBAAiB2F,YAAY1F,cAAc;gBAC/D,MAAM,EAAE+L,WAAW,EAAE,GACnB7L,QAAQ;gBACV,8EAA8E;gBAC9E,gFAAgF;gBAChF,qFAAqF;gBACrF,MAAM4C,OAAO,MAAMiJ,YAAYnD;gBAC/B,MAAMoD,OAAO;oBACXC,WAAWnJ,KAAKsC,KAAK;oBACrB8G,YAAYpJ,KAAK4F,MAAM;oBACvByD,aAAa,CAAC,KAAK,EAAE1D,YAAY,QAAQ,EAAEG,gBAAgB3D,QAAQ,CACjE,UACA,CAAC;gBACL;gBACA2D,kBAAkB4B,OAAOC,IAAI,CAAC2B,SAAS/N,gBAAgB2N;gBACvDvD,cAAc;YAChB;YACA,OAAO;gBACLxG,QAAQ2G;gBACRH;gBACA1G,QAAQtB,KAAKE,GAAG,CAACoB,QAAQuB,WAAWG,MAAM,CAACI,eAAe;YAC5D;QACF,OAAO;YACL,MAAM,IAAI2D,WAAW,KAAK;QAC5B;IACF,EAAE,OAAOD,OAAO;QACd,IAAIoE,kBAAkBC,cAAc;YAClC,yDAAyD;YACzD,OAAO;gBACL3J,QAAQ0J;gBACRlD,aAAamD;gBACb7J,QAAQuB,WAAWG,MAAM,CAACI,eAAe;YAC3C;QACF,OAAO;YACL,MAAM,IAAI2D,WACR,KACA;QAEJ;IACF;AACF;AAEA,SAAS6E,yBACPnI,GAAW,EACXuE,WAA0B;IAE1B,MAAM,CAAC6D,sBAAsB,GAAGpI,IAAIyC,KAAK,CAAC,KAAK;IAC/C,MAAM4F,wBAAwBD,sBAAsB3F,KAAK,CAAC,KAAK6F,GAAG;IAClE,IAAI,CAAC/D,eAAe,CAAC8D,uBAAuB;QAC1C,OAAO;IACT;IAEA,MAAM,CAACE,SAAS,GAAGF,sBAAsB5F,KAAK,CAAC,KAAK;IACpD,MAAM7E,YAAYnD,aAAa8J;IAC/B,OAAO,CAAC,EAAEgE,SAAS,CAAC,EAAE3K,UAAU,CAAC;AACnC;AAEA,SAAS4K,mBACPtJ,GAAoB,EACpBgH,GAAmB,EACnBlG,GAAW,EACXhC,IAAY,EACZuG,WAA0B,EAC1B7C,QAAiB,EACjB+G,MAAoB,EACpBC,YAAiC,EACjC7K,MAAc,EACdwB,KAAc;IAEd6G,IAAIyC,SAAS,CAAC,QAAQ;IACtBzC,IAAIyC,SAAS,CACX,iBACAjH,WACI,yCACA,CAAC,gBAAgB,EAAErC,QAAQ,IAAIxB,OAAO,iBAAiB,CAAC;IAE9D,IAAItD,iBAAiB2E,KAAKgH,KAAKlI,OAAO;QACpC,6CAA6C;QAC7C,OAAO;YAAE4K,UAAU;QAAK;IAC1B;IACA,IAAIrE,aAAa;QACf2B,IAAIyC,SAAS,CAAC,gBAAgBpE;IAChC;IAEA,MAAMgE,WAAWJ,yBAAyBnI,KAAKuE;IAC/C2B,IAAIyC,SAAS,CACX,uBACA/O,mBAAmB2O,UAAU;QAAE3C,MAAM8C,aAAaG,sBAAsB;IAAC;IAG3E3C,IAAIyC,SAAS,CAAC,2BAA2BD,aAAaI,qBAAqB;IAC3E5C,IAAIyC,SAAS,CAAC,kBAAkBF;IAEhC,OAAO;QAAEG,UAAU;IAAM;AAC3B;AAEA,OAAO,SAASG,aACd7J,GAAoB,EACpBgH,GAAmB,EACnBlG,GAAW,EACXpC,SAAiB,EACjBG,MAAc,EACd2D,QAAiB,EACjB+G,MAAoB,EACpBC,YAAiC,EACjC7K,MAAc,EACdwB,KAAc;IAEd,MAAMkF,cAAc/J,eAAeoD;IACnC,MAAMI,OAAOd,QAAQ;QAACa;KAAO;IAC7B,MAAMiL,SAASR,mBACbtJ,KACAgH,KACAlG,KACAhC,MACAuG,aACA7C,UACA+G,QACAC,cACA7K,QACAwB;IAEF,IAAI,CAAC2J,OAAOJ,QAAQ,EAAE;QACpB1C,IAAIyC,SAAS,CAAC,kBAAkBrC,OAAO2C,UAAU,CAAClL;QAClDmI,IAAIgD,GAAG,CAACnL;IACV;AACF;AAEA,OAAO,eAAeoL,aACpBpL,MAAc,EACd,8BAA8B;AAC9BH,SAA2C;IAK3C,qDAAqD;IACrD,0DAA0D;IAC1D,IAAIA,cAAc,QAAQ;QACxB,IAAI7B,OAAO;YACT,MAAM4I,cAAc5I,MAAMgC;YAC1B,MAAM,EAAEmD,KAAK,EAAEsD,MAAM,EAAE,GAAG,MAAMG,YAAY9F,QAAQ;YACpD,OAAO;gBAAEqC;gBAAOsD;YAAO;QACzB,OAAO;YACL,MAAM,EAAE4E,YAAY,EAAE,GACpBpN,QAAQ;YACV,MAAM,EAAEkF,KAAK,EAAEsD,MAAM,EAAE,GAAG,MAAM4E,aAAarL;YAC7C,OAAO;gBAAEmD;gBAAOsD;YAAO;QACzB;IACF;IAEA,MAAM,EAAEtD,KAAK,EAAEsD,MAAM,EAAE,GAAGzK,YAAYgE;IACtC,OAAO;QAAEmD;QAAOsD;IAAO;AACzB"}