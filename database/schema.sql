create extension if not exists "uuid-ossp";

create table if not exists profiles (
  id uuid primary key,
  email text unique,
  created_at timestamptz default now()
);

create table if not exists queries (
  id uuid primary key default uuid_generate_v4(),
  user_id uuid not null references profiles(id) on delete cascade,
  address text not null,
  status text not null check (status in ('pending','running','done','failed')),
  provider text,
  model text,
  idempotency_key text,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

create table if not exists media (
  id uuid primary key default uuid_generate_v4(),
  query_id uuid not null references queries(id) on delete cascade,
  role text not null,
  storage_path text not null,
  width int,
  height int,
  bytes int,
  mime text,
  created_at timestamptz default now()
);

create table if not exists results (
  query_id uuid primary key references queries(id) on delete cascade,
  orientation_guess text,
  confidence numeric,
  pros jsonb,
  risks jsonb,
  suggestions jsonb,
  importance_score int,
  photo_requests jsonb,
  disclaimers jsonb,
  raw_response jsonb,
  created_at timestamptz default now()
);

alter table profiles enable row level security;
alter table queries  enable row level security;
alter table media    enable row level security;
alter table results  enable row level security;

create or replace function public.handle_new_user()
returns trigger as $$
begin
  insert into public.profiles (id, email)
  values (new.id, new.email)
  on conflict (id) do nothing;
  return new;
end;
$$ language plpgsql security definer;

drop trigger if exists on_auth_user_created on auth.users;
create trigger on_auth_user_created
after insert on auth.users
for each row execute function public.handle_new_user();

drop policy if exists "profiles sel own" on profiles;
create policy "profiles sel own" on profiles
for select using (id = auth.uid());

drop policy if exists "queries ins own" on queries;
create policy "queries ins own" on queries
for insert with check (user_id = auth.uid());

drop policy if exists "queries sel own" on queries;
create policy "queries sel own" on queries
for select using (user_id = auth.uid());

drop policy if exists "queries upd own" on queries;
create policy "queries upd own" on queries
for update using (user_id = auth.uid());

drop policy if exists "media ins own" on media;
create policy "media ins own" on media
for insert with check (
  exists(select 1 from queries q where q.id = query_id and q.user_id = auth.uid())
);

drop policy if exists "media sel own" on media;
create policy "media sel own" on media
for select using (
  exists(select 1 from queries q where q.id = query_id and q.user_id = auth.uid())
);

drop policy if exists "results sel own" on results;
create policy "results sel own" on results
for select using (
  exists(select 1 from queries q where q.id = query_id and q.user_id = auth.uid())
);

drop policy if exists "results ins own" on results;
create policy "results ins own" on results
for insert with check (
  exists(select 1 from queries q where q.id = query_id and q.user_id = auth.uid())
);