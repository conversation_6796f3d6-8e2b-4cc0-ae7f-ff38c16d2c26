{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "noEmit": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["*"]}, "types": ["node"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}