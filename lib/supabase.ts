import { createClient } from "@supabase/supabase-js";
import { ENV } from "@/lib/env";

export const supabase = createClient(
    ENV.NEXT_PUBLIC_SUPABASE_URL,
    ENV.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    { auth: { persistSession: false } }
);

export const supabaseAdmin = createClient(
    ENV.NEXT_PUBLIC_SUPABASE_URL,
    ENV.SUPABASE_SERVICE_ROLE_KEY,
    { auth: { persistSession: false } }
);

export const STORAGE_BUCKETS = {
    IMAGES: ENV.SUPABASE_BUCKET
} as const;