// lib/env.ts
function need(name: string): string {
    const v = process.env[name];
    if (!v) throw new Error(`Missing required environment variable ${name}`);
    return v;
}

export const ENV = {
    NEXT_PUBLIC_SUPABASE_URL: need("NEXT_PUBLIC_SUPABASE_URL"),
    NEXT_PUBLIC_SUPABASE_ANON_KEY: need("NEXT_PUBLIC_SUPABASE_ANON_KEY"),
    SUPABASE_SERVICE_ROLE_KEY: need("SUPABASE_SERVICE_ROLE_KEY"),
    OPENAI_API_KEY: need("OPENAI_API_KEY"),
    SUPABASE_BUCKET: process.env.SUPABASE_BUCKET || "fengshui-uploads"
};