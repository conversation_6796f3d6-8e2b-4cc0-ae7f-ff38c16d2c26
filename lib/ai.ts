import OpenAI from "openai";
import { z } from "zod";
import { ENV } from "@/lib/env";

const client = new OpenAI({ apiKey: ENV.OPENAI_API_KEY });

export const ResultSchema = z.object({
    orientationGuess: z.string(),
    confidence: z.number().min(0).max(1),
    pros: z.array(z.string()).default([]),
    risks: z.array(z.string()).default([]),
    suggestions: z.array(z.string()).default([]),
    importanceScore: z.number().int().min(0).max(10),
    photoRequests: z.array(
        z.object({ role: z.string(), instruction: z.string() })
    ).default([]),
    disclaimers: z.array(z.string()).default([])
});
export type FengResult = z.infer<typeof ResultSchema>;

type MediaItem = { role: string; url: string };

function buildPrompt(address: string, media: MediaItem[]) {
    const lines: string[] = [];
    lines.push("你是一位专业风水顾问。基于地址与多张房屋照片，进行审慎分析。信息不足时请明确指出需要补充的照片角度。");
    lines.push(`地址: ${address}`);
    lines.push("图片列表:");
    media.forEach(m => lines.push(`- ${m.role}: ${m.url}`));
    lines.push("请输出严格的 UTF8 JSON，字段为");
    lines.push("{");
    lines.push('"orientationGuess": "...",');
    lines.push('"confidence": 0.0,');
    lines.push('"pros": ["..."],');
    lines.push('"risks": ["..."],');
    lines.push('"suggestions": ["..."],');
    lines.push('"importanceScore": 0,');
    lines.push('"photoRequests": [{"role":"fromDoorOut","instruction":"..."}, {"role":"frontDoor","instruction":"..."}, {"role":"overhead","instruction":"..."}],');
    lines.push('"disclaimers": ["..."]');
    lines.push("}");
    return lines.join("\n");
}

export async function runFengshui(address: string, media: MediaItem[]): Promise<FengResult> {
    const prompt = buildPrompt(address, media);
    const content: any[] = [{ type: "text", text: prompt }, ...media.map(m => ({ type: "image_url", image_url: { url: m.url } }))];

    const res = await client.chat.completions.create({
        model: "gpt-4o-mini",
        temperature: 0.2,
        messages: [
            { role: "system", content: "仅输出严格的 JSON；如果无法确定，请在 JSON 中清楚描述不确定点。" },
            { role: "user", content }
        ],
        response_format: { type: "json_object" }
    });

    const raw = res.choices[0]?.message?.content ?? "{}";
    const parsed = JSON.parse(raw);
    return ResultSchema.parse(parsed);
}