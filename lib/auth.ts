import { NextRequest } from "next/server";
import { supabase } from "@/lib/supabase";

export async function verifyAuth(request: NextRequest) {
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.toLowerCase().startsWith("bearer ")) {
        return { error: "No valid token provided", user: null };
    }
    const token = authHeader.slice(7);
    const { data, error } = await supabase.auth.getUser(token);
    if (error || !data.user) {
        return { error: "Authentication failed", user: null };
    }
    return { error: null, user: data.user };
}

export function createAuthResponse(error: string, status = 401) {
    return new Response(JSON.stringify({ error }), {
        status,
        headers: { "Content-Type": "application/json" }
    });
}