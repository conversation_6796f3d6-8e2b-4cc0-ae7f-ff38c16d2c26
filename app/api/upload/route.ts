import { NextRequest, NextResponse } from "next/server";
import { supabaseAdmin, STORAGE_BUCKETS } from "@/lib/supabase";
import { verifyAuth, createAuthResponse } from "@/lib/auth";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
    const { error: authError, user } = await verifyAuth(request);
    if (authError || !user) return createAuthResponse(authError || "Unauthorized");

    try {
        const form = await request.formData();
        const file = form.get("file") as File | null;
        const role = String(form.get("role") || "other");
        const queryId = String(form.get("queryId") || "");
        if (!file || !queryId) {
            return NextResponse.json({ error: "missing file or queryId" }, { status: 400 });
        }

        const bytes = Buffer.from(await file.arrayBuffer());
        const ext = file.type.includes("png") ? "png" : file.type.includes("webp") ? "webp" : "jpg";
        const key = `${user.id}/${queryId}/${Date.now()}_${Math.random().toString(36).slice(2)}.${ext}`;

        const { error: upErr } = await supabaseAdmin
            .storage.from(STORAGE_BUCKETS.IMAGES)
            .upload(key, bytes, { contentType: file.type, upsert: false });
        if (upErr) return NextResponse.json({ error: upErr.message }, { status: 500 });

        const { error: mErr } = await supabaseAdmin
            .from("media")
            .insert({ query_id: queryId, role, storage_path: key, bytes: bytes.byteLength, mime: file.type });
        if (mErr) return NextResponse.json({ error: mErr.message }, { status: 500 });

        return NextResponse.json({ ok: true, path: key, role });
    } catch (e: any) {
        return NextResponse.json({ error: e.message || "upload failed" }, { status: 500 });
    }
}