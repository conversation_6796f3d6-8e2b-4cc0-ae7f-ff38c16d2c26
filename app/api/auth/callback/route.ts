import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')

    if (code) {
        const { data, error } = await supabase.auth.exchangeCodeForSession(code)

        if (error) {
            console.error('Auth callback error:', error)
            return NextResponse.redirect(`${request.nextUrl.origin}/login?error=auth_failed`)
        }

        // Successful authentication
        if (data.user) {
            // You can redirect to your app's success page or return token info
            return NextResponse.redirect(`${request.nextUrl.origin}/dashboard`)
        }
    }

    // No code or authentication failed
    return NextResponse.redirect(`${request.nextUrl.origin}/login?error=no_code`)
}

// Handle POST requests for token refresh
export async function POST(request: NextRequest) {
    try {
        const { refresh_token } = await request.json()

        if (!refresh_token) {
            return NextResponse.json(
                { error: 'Refresh token required' },
                { status: 400 }
            )
        }

        const { data, error } = await supabase.auth.refreshSession({
            refresh_token
        })

        if (error) {
            return NextResponse.json(
                { error: 'Failed to refresh token' },
                { status: 401 }
            )
        }

        return NextResponse.json({
            access_token: data.session?.access_token,
            refresh_token: data.session?.refresh_token,
            user: data.user
        })

    } catch (error) {
        console.error('Token refresh error:', error)
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        )
    }
}