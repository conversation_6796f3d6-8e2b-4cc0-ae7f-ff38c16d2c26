import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { supabaseAdmin, STORAGE_BUCKETS } from "@/lib/supabase";
import { verifyAuth, createAuthResponse } from "@/lib/auth";
import { runFengshui, ResultSchema } from "@/lib/ai";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

const CreateSchema = z.object({
    address: z.string().min(3)
});

export async function POST(request: NextRequest) {
    const { error: authError, user } = await verifyAuth(request);
    if (authError || !user) return createAuthResponse(authError || "Unauthorized");

    try {
        const body = await request.json();
        const { address } = CreateSchema.parse(body);

        const { data: q, error: qErr } = await supabaseAdmin
            .from("queries")
            .insert({ user_id: user.id, address, status: "running", provider: "openai", model: "gpt-4o-mini" })
            .select("*")
            .single();
        if (qErr) return NextResponse.json({ error: qErr.message }, { status: 500 });

        const { data: files, error: mErr } = await supabaseAdmin
            .from("media")
            .select("*")
            .eq("query_id", q.id);
        if (mErr) return NextResponse.json({ error: mErr.message }, { status: 500 });

        const mediaForModel: { role: string; url: string }[] = [];
        for (const f of files || []) {
            const signed = await supabaseAdmin
                .storage.from(STORAGE_BUCKETS.IMAGES)
                .createSignedUrl(f.storage_path, 60 * 30);
            if (signed.data?.signedUrl) mediaForModel.push({ role: f.role, url: signed.data.signedUrl });
        }

        const result = await runFengshui(address, mediaForModel);
        ResultSchema.parse(result);

        const { error: rErr } = await supabaseAdmin
            .from("results")
            .insert({
                query_id: q.id,
                orientation_guess: result.orientationGuess,
                confidence: result.confidence,
                pros: result.pros,
                risks: result.risks,
                suggestions: result.suggestions,
                importance_score: result.importanceScore,
                photo_requests: result.photoRequests,
                disclaimers: result.disclaimers,
                raw_response: result
            });
        if (rErr) return NextResponse.json({ error: rErr.message }, { status: 500 });

        await supabaseAdmin.from("queries").update({ status: "done", updated_at: new Date().toISOString() }).eq("id", q.id);

        return NextResponse.json({ ok: true, queryId: q.id });
    } catch (e: any) {
        return NextResponse.json({ error: e.message || "analyze failed" }, { status: 400 });
    }
}

export async function GET(request: NextRequest) {
    const { error: authError, user } = await verifyAuth(request);
    if (authError || !user) return createAuthResponse(authError || "Unauthorized");

    try {
        const { searchParams } = new URL(request.url);
        const id = searchParams.get("id");
        if (!id) {
            const { data, error } = await supabaseAdmin
                .from("queries")
                .select("id,address,status,created_at,updated_at")
                .eq("user_id", user.id)
                .order("created_at", { ascending: false })
                .limit(50);
            if (error) throw error;
            return NextResponse.json({ items: data || [] });
        }

        const { data: q } = await supabaseAdmin.from("queries").select("*").eq("id", id).single();
        const { data: r } = await supabaseAdmin.from("results").select("*").eq("query_id", id).single();
        const { data: m } = await supabaseAdmin.from("media").select("id,role,storage_path,bytes,mime,created_at").eq("query_id", id);

        return NextResponse.json({ query: q, result: r, media: m || [] });
    } catch (e: any) {
        return NextResponse.json({ error: e.message || "fetch failed" }, { status: 400 });
    }
}